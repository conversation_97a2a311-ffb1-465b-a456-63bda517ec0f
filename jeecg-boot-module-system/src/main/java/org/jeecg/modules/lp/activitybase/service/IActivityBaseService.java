package org.jeecg.modules.lp.activitybase.service;

import org.jeecg.modules.lp.activitybase.entity.ActivityBase;
import org.jeecg.modules.lp.activitybase.entity.ActivityBaseDetailVo;
import org.jeecg.modules.lp.activitybase.entity.ActivityBaseListVo;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 公益活动-劳动基地
 * @Author: LiuQC
 * @Date:   2024-04-08
 * @Version: V1.0
 */
public interface IActivityBaseService extends IService<ActivityBase> {

    /**
     * 定时更新参与人数和次数
     */
    void updateActivityBaseCount();

    /**
     * 获取劳动基地详情，包含当前正在劳动的人员信息
     * @param baseId 劳动基地ID
     * @return 劳动基地详情
     */
    ActivityBaseDetailVo getLaborBaseDetail(String baseId);

    /**
     * 分页查询劳动基地列表，包含当前活动人数
     * @param pageNo 页码
     * @param pageSize 页大小
     * @return 劳动基地列表分页数据
     */
    IPage<ActivityBaseListVo> getLaborBaseListWithCurrentCount(Integer pageNo, Integer pageSize);

}
