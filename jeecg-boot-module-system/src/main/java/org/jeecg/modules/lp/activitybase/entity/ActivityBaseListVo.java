package org.jeecg.modules.lp.activitybase.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 劳动基地列表VO
 * @Author: AI Assistant
 * @Date: 2025-08-04
 * @Version: V1.0
 */
@Data
@ApiModel(value = "ActivityBaseListVo", description = "劳动基地列表VO")
public class ActivityBaseListVo {

    /**
     * 基地ID
     */
    @ApiModelProperty(value = "基地ID")
    private String id;

    /**
     * 劳动基地名称
     */
    @ApiModelProperty(value = "劳动基地名称")
    private String baseName;

    /**
     * 矫正单位
     */
    @ApiModelProperty(value = "矫正单位")
    private String jzjgName;

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    private String address;

    /**
     * 活动特色
     */
    @ApiModelProperty(value = "活动特色")
    private String eventFeature;

    /**
     * 开放时间-开始
     */
    @ApiModelProperty(value = "开放时间-开始")
    private String openTime;

    /**
     * 开放时间-结束
     */
    @ApiModelProperty(value = "开放时间-结束")
    private String closeTime;

    /**
     * 基地类别
     */
    @ApiModelProperty(value = "基地类别")
    private String baseType;

    /**
     * 可容纳人数
     */
    @ApiModelProperty(value = "可容纳人数")
    private Integer maxPerson;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private String personCharge;

    /**
     * 联系方式
     */
    @ApiModelProperty(value = "联系方式")
    private String telephone;

    /**
     * 开展次数
     */
    @ApiModelProperty(value = "开展次数")
    private Integer launchNumber;

    /**
     * 参加总人数
     */
    @ApiModelProperty(value = "参加总人数")
    private Integer totalParticipate;

    /**
     * 当前活动人数（今日已签到但未签退的人员数量）
     */
    @ApiModelProperty(value = "当前活动人数")
    private Integer currentActivityCount;

    /**
     * 经度
     */
    @ApiModelProperty(value = "经度")
    private String longitude;

    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度")
    private String latitude;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
